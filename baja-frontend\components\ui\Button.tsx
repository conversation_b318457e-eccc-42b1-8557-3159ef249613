import React from 'react';
import { cn } from '@/lib/utils';
import Spinner from './Spinner';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref) => {
    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-yellow-500 focus-visible:ring-offset-2 focus-visible:ring-offset-black disabled:opacity-50 disabled:pointer-events-none';
    
    const variants = {
      primary: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-black hover:from-yellow-400 hover:to-yellow-500 hover:shadow-lg hover:shadow-yellow-500/30 font-semibold',
      secondary: 'bg-black text-yellow-500 border-2 border-yellow-500 hover:bg-yellow-500 hover:text-black hover:shadow-lg hover:shadow-yellow-500/30',
      success: 'bg-green-600 text-white hover:bg-green-700 hover:shadow-lg hover:shadow-green-500/30',
      danger: 'bg-red-600 text-white hover:bg-red-700 hover:shadow-lg hover:shadow-red-500/30',
      warning: 'bg-yellow-600 text-black hover:bg-yellow-700 hover:shadow-lg hover:shadow-yellow-500/30',
      outline: 'border-2 border-yellow-500 bg-transparent text-yellow-500 hover:bg-yellow-500 hover:text-black hover:shadow-lg hover:shadow-yellow-500/30',
      ghost: 'text-yellow-500 hover:bg-yellow-500/10 hover:text-yellow-400',
    };

    const sizes = {
      sm: 'h-9 px-3 text-sm',
      md: 'h-10 py-2 px-4',
      lg: 'h-11 px-8 text-lg',
    };

    return (
      <button
        className={cn(
          baseClasses,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <Spinner size="sm" color="primary" className="mr-2" />
        )}
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;