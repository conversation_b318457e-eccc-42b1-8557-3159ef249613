'use client';

import React, { useState, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Gallery } from '@/types';
import { uploadService } from '@/lib/upload.service';
import api from '@/lib/api';

const GalleryPage = () => {
  const [gallery, setGallery] = useState<Gallery[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState<Gallery | null>(null);

  useEffect(() => {
    const fetchGallery = async () => {
      try {
        const response = await api.get('/gallery/active');
        if (response.data.success && response.data.data) {
          setGallery(response.data.data);
        } else {
          console.error('Failed to fetch gallery:', response.data.message);
          setGallery([]);
        }
      } catch (error) {
        console.error('Error fetching gallery:', error);
        setGallery([]);
      } finally {
        setLoading(false);
      }
    };

    fetchGallery();
  }, []);

  const openModal = (image: Gallery) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  // Transform gallery data for CircularGallery component
  const circularGalleryImages = gallery.map((item) => ({
    src: item.images ? uploadService.getOptimizedUrl(item.images) : '/placeholder-image.jpg',
    alt: item.description || `Gallery #${item.id}`,
    title: `Gallery #${item.id}`,
    description: item.description,
    onClick: () => openModal(item),
  }));

  return (
    <div className="min-h-screen bg-black">
      <Navbar />

      <div className="pt-16">
        {/* Header */}
        <div className="bg-gradient-to-r from-black via-gray-900 to-black border-b border-gold-500">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <h1 className="text-4xl md:text-5xl font-bold text-white text-center mb-4">
              Gallery
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-gold-400 to-gold-600 mx-auto mb-6"></div>
            <p className="text-xl text-gray-300 text-center max-w-3xl mx-auto">
              Dokumentasi momen-momen terbaik dari berbagai event olahraga bela diri
            </p>
          </div>
        </div>

        

        {/* Additional Grid View (Optional) */}
        {!loading && gallery.length > 0 && (
          <div className="bg-black-900 py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {gallery.map((item) => (
                  <div
                    key={item.id}
                    className="group cursor-pointer transform transition-all duration-300 hover:scale-105"
                    onClick={() => openModal(item)}
                  >
                    <div className="aspect-square overflow-hidden rounded-lg bg-gray-800 border-2 border-transparent hover:border-gold-500 transition-colors duration-300">
                      <img
                        src={item.images ? uploadService.getOptimizedUrl(item.images) : '/placeholder-image.jpg'}
                        alt={item.description || 'Gallery Image'}
                        className="h-full w-full object-cover group-hover:opacity-90 transition-opacity duration-300"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder-image.jpg';
                        }}
                      />
                    </div>
                    <div className="mt-3">
                      <h3 className="text-sm font-semibold text-white group-hover:text-gold-400 transition-colors duration-300">
                        Gallery #{item.id}
                      </h3>
                      {item.description && (
                        <p className="text-xs text-gray-400 mt-1 line-clamp-2">
                          {item.description}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
      {/* Enhanced Modal */}
      {selectedImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 backdrop-blur-sm">
          <div className="relative max-w-4xl max-h-full p-4">
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 bg-black bg-opacity-50 rounded-full p-2 text-white hover:text-gold-400 hover:bg-opacity-75 transition-all duration-300"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="bg-gray-900 rounded-lg overflow-hidden border border-gold-500 shadow-2xl">
              <img
                src={selectedImage.images ? uploadService.getOptimizedUrl(selectedImage.images) : '/placeholder-image.jpg'}
                alt={selectedImage.description || 'Gallery Image'}
                className="max-w-full max-h-[70vh] object-contain mx-auto block"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder-image.jpg';
                }}
              />

              <div className="bg-gradient-to-t from-black via-gray-900 to-transparent p-6">
                <h3 className="text-white text-xl font-bold mb-2">
                  Gallery #{selectedImage.id}
                </h3>
                {selectedImage.description && (
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {selectedImage.description}
                  </p>
                )}
                <div className="flex items-center justify-between mt-4">
                  <span className="text-gold-400 text-sm font-semibold">
                    Martial Arts Gallery
                  </span>
                  <button
                    onClick={closeModal}
                    className="bg-gold-500 hover:bg-gold-600 text-black px-4 py-2 rounded-lg text-sm font-semibold transition-colors duration-300"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default GalleryPage;
